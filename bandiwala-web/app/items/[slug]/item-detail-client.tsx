'use client';

import { useState, useEffect } from 'react';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Minus, Plus } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Image from 'next/image';
import { toast } from 'sonner';
import { menuItemService } from '@/services/api';
import { getImageUrl } from '@/utils/imageUtils';
import ReviewsList from '@/components/reviews/ReviewsList';
import useUserLocation from '@/hooks/useUserLocation';

interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

interface MenuItem {
  _id: string;
  itemName: string;
  slug: string;
  description: string;
  subcategories: Subcategory[];
  image: string;
  itemCategory: string;
  vendorId: string;
  isAvailable: boolean;
}

interface ItemDetailClientProps {
  slug: string;
}

export default function ItemDetailClient({ slug }: ItemDetailClientProps) {
  const { addToCart } = useCart();
  const { user, isAuthenticated } = useAuth();
  const { coordinates } = useUserLocation();
  const [quantity, setQuantity] = useState(1);
  const [selectedSubcategory, setSelectedSubcategory] = useState<Subcategory | null>(null);
  const [item, setItem] = useState<MenuItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);

  const itemIdentifier = slug || '';

  // Add mobile-specific debugging
  useEffect(() => {
    console.log('=== MOBILE DEBUG INFO ===');
    console.log('User Agent:', navigator.userAgent);
    console.log('Screen width:', window.innerWidth);
    console.log('Screen height:', window.innerHeight);
    console.log('Device pixel ratio:', window.devicePixelRatio);
    console.log('Slug received:', slug);
    console.log('Item identifier:', itemIdentifier);
    console.log('=== END MOBILE DEBUG ===');
  }, [slug, itemIdentifier]);

  // Get image URL - use the getImageUrl utility function like FoodCard does
  const getImageSrc = () => {
    // If there's an image error, use local fallback
    if (imageError) {
      return '/images/vendor1.jpg';
    }

    // If no item image, use local fallback
    if (!item?.image) {
      return '/images/vendor1.jpg';
    }

    // Use the same getImageUrl utility function that other components use
    return getImageUrl(item.image);
  };

  useEffect(() => {
    const fetchItem = async () => {
      console.log('=== FETCH ITEM START ===');
      console.log('Item identifier:', itemIdentifier);
      console.log('Is mobile device:', window.innerWidth < 768);

      if (!itemIdentifier) {
        console.error('No item identifier provided');
        setError('No item identifier provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null); // Clear any previous errors
        let response;

        // Check if the parameter looks like a MongoDB ObjectId (24 hex characters)
        const isObjectId = /^[0-9a-fA-F]{24}$/.test(itemIdentifier);

        console.log('Is ObjectId:', isObjectId);

        if (isObjectId) {
          // If it's an ObjectId, fetch by ID
          console.log('Fetching item by ID:', itemIdentifier);
          response = await menuItemService.getMenuItemById(itemIdentifier);
        } else {
          // If it's not an ObjectId, treat it as a slug
          console.log('Fetching item by slug:', itemIdentifier);
          response = await menuItemService.getMenuItemBySlug(itemIdentifier);
        }

        console.log('API Response received:', response);

        if (response.success && response.data) {
          console.log('=== ITEM FETCH SUCCESS ===');
          console.log('Full response:', response);
          console.log('Item data:', response.data);
          console.log('Item image field:', response.data.image);
          console.log('Item image type:', typeof response.data.image);
          console.log('Is mobile device:', window.innerWidth < 768);
          console.log('=== END ITEM FETCH ===');
          setItem(response.data);
          // Set default selected subcategory to the first one
          if (response.data.subcategories && response.data.subcategories.length > 0) {
            setSelectedSubcategory(response.data.subcategories[0]);
          }
        } else {
          console.error('Item not found in response:', response);
          setError('Item not found');
        }
      } catch (err) {
        console.error('Error fetching item:', err);
        console.error('Error details:', {
          message: err.message,
          stack: err.stack,
          itemIdentifier,
          isMobile: window.innerWidth < 768
        });
        setError(`Failed to load item: ${err.message}`);
      } finally {
        setLoading(false);
        console.log('=== FETCH ITEM END ===');
      }
    };

    fetchItem();
  }, [itemIdentifier]);

  const handleAddToCart = () => {
    // Check authentication before allowing cart operations
    if (!isAuthenticated) {
      toast("Please login to add items to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      return;
    }

    if (!item || !selectedSubcategory) {
      toast("Please select a plate size", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      return;
    }

    try {
      if (!item._id || !item.itemName || !selectedSubcategory.price || !item.image || !item.vendorId) {
        console.error('Missing required properties for adding to cart:', item);
        return;
      }

      try {
        const localCartKey = 'bandiwala-cart';
        const localCartJson = localStorage.getItem(localCartKey);
        console.log(localCartJson);
        const localCart = localCartJson ? JSON.parse(localCartJson) : { userId: 'guest-user', items: [] };

        const existingItemIndex = localCart.items.findIndex(
          (cartItem: any) => cartItem.menuItemId === item._id
        );

        // If the item doesn't exist in the cart, add its details to local storage
        if (existingItemIndex === -1) {
          // Store item details in local storage for display purposes
          const itemDetails = {
            menuItemId: item._id,
            name: item.itemName,
            selectedSubcategory: selectedSubcategory,
            image: item.image,
            vendorId: item.vendorId,
            vendorName: 'Unknown Vendor' // We'll need to fetch vendor name separately or include it in the API
          };

          // Save these details in a separate storage key for reference
          localStorage.setItem(`item-details-${item._id}`, JSON.stringify(itemDetails));
        }
      } catch (storageError) {
        console.error('Error updating local storage:', storageError);
      }

      // Call addToCart with the correct parameters including selected subcategory and user location
      addToCart(item._id, quantity, '', selectedSubcategory, coordinates || undefined);
    } catch (error) {
      console.error('Error adding item to cart:', error);
      toast("Failed to add item to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
    }
  };

  if (loading) {
    return (
      <>
        <Header />
        <main className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-bandiwala-orange mx-auto mb-4"></div>
              <p className="text-gray-600">Loading item details...</p>
              <p className="text-xs text-gray-400 mt-2">Slug: {itemIdentifier}</p>
              <p className="text-xs text-gray-400">Device: {typeof window !== 'undefined' && window.innerWidth < 768 ? 'Mobile' : 'Desktop'}</p>
            </div>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  if (error || !item) {
    return (
      <>
        <Header showBackButton={true} />
        <main className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-800 mb-4">Item Not Found</h1>
              <p className="text-gray-600 mb-4">{error || 'The item you are looking for does not exist.'}</p>
              <div className="text-xs text-gray-400 mb-4 space-y-1">
                <p>Slug: {itemIdentifier}</p>
                <p>Device: {typeof window !== 'undefined' && window.innerWidth < 768 ? 'Mobile' : 'Desktop'}</p>
                <p>Screen: {typeof window !== 'undefined' ? `${window.innerWidth}x${window.innerHeight}` : 'Unknown'}</p>
              </div>
              <Button
                onClick={() => window.history.back()}
                className="bg-bandiwala-orange hover:bg-bandiwala-red text-white"
              >
                Go Back
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header showBackButton={true} />
      <main className="bg-white">
        {/* Mobile-First Layout */}
        <div className="block lg:hidden">
          {/* Compact Image Section for Mobile */}
          <div className="relative h-[35vh] w-full bg-gradient-to-br from-gray-100 to-gray-200 flex-shrink-0 flex items-center justify-center">
            {/* Fallback content when no image */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-gray-400">
                <div className="w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🍽️</span>
                </div>
                <p className="text-sm">Food Image</p>
              </div>
            </div>

            <Image
              src={getImageSrc()}
              alt={item.itemName}
              fill
              className="object-cover z-10"
              priority
              onError={() => {
                console.log('Mobile image failed to load, using fallback');
                if (!imageError) {
                  setImageError(true);
                }
              }}
              onLoad={() => console.log('Mobile image loaded successfully')}
            />
            <div className="absolute top-3 left-3 bg-white px-3 py-1.5 rounded-full text-xs font-bold text-bandiwala-red shadow-lg z-20">
              {item.itemCategory || 'Special'}
            </div>
          </div>

          {/* Mobile Content */}
          <div className="p-4">
            <div className="space-y-4">
              {/* Title and Price Row */}
              <div className="flex justify-between items-start">
                <div className="flex-1 pr-3">
                  <h1 className="text-2xl font-bold text-gray-900 leading-tight mb-2">
                    {item.itemName}
                  </h1>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <p className="text-3xl font-bold bg-gradient-to-r from-bandiwala-orange to-bandiwala-red bg-clip-text text-transparent">
                    ₹{selectedSubcategory?.price || 0}
                  </p>
                </div>
              </div>

              {/* Plate Size Selector */}
              <div className="space-y-3">
                <label className="block text-base font-semibold text-gray-800">
                  Select Plate Size
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {item.subcategories.map((subcategory, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                        selectedSubcategory?.title === subcategory.title
                          ? 'bg-bandiwala-orange text-white shadow-md'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => setSelectedSubcategory(subcategory)}
                    >
                      <div className="text-center">
                        <div className="font-semibold">{subcategory.title}</div>
                        <div className="text-xs opacity-75">{subcategory.quantity}</div>
                        <div className="font-bold">₹{subcategory.price}</div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Quantity and Add to Cart */}
              <div className="flex items-center gap-3 pt-6">
                <div className="flex items-center border-2 border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
                  <button
                    className="p-3 hover:bg-gray-50 transition-colors"
                    onClick={() => setQuantity(q => Math.max(1, q - 1))}
                  >
                    <Minus size={20} className="text-gray-600" />
                  </button>
                  <span className="px-6 py-3 font-semibold text-xl min-w-[60px] text-center">
                    {quantity}
                  </span>
                  <button
                    className="p-3 hover:bg-gray-50 transition-colors"
                    onClick={() => setQuantity(q => q + 1)}
                  >
                    <Plus size={20} className="text-gray-600" />
                  </button>
                </div>

                <Button
                  className="flex-1 bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white px-8 py-3 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                  onClick={handleAddToCart}
                >
                  Add to Cart
                </Button>
              </div>
            </div>

            {/* Reviews Section for Mobile */}
            {item && (
              <ReviewsList
                targetType="MenuItem"
                targetId={item._id}
                targetName={item.itemName}
                currentUserId={user?._id}
                isAuthenticated={isAuthenticated}
              />
            )}
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          {/* Top Section - Side by Side */}
          <div className="grid lg:grid-cols-2 min-h-[calc(100vh-4rem)]">
            {/* Image Section */}
            <div className="relative h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              {/* Fallback content when no image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-gray-400">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-4xl">🍽️</span>
                  </div>
                  <p className="text-lg">Food Image</p>
                </div>
              </div>

              <Image
                src={getImageSrc()}
                alt={item.itemName}
                fill
                className="object-cover z-10"
                priority
                onError={() => {
                  console.log('Desktop image failed to load, using fallback');
                  if (!imageError) {
                    setImageError(true);
                  }
                }}
                onLoad={() => console.log('Desktop image loaded successfully')}
              />
              <div className="absolute top-6 left-6 bg-white px-4 py-2 rounded-full text-sm font-bold text-bandiwala-red shadow-lg z-20">
                {item.itemCategory || 'Special'}
              </div>
            </div>

            {/* Content Section */}
            <div className="p-12 xl:p-16 flex flex-col justify-center">
              <div className="space-y-8 max-w-lg mx-auto lg:max-w-none">
                {/* Title and Description */}
                <div>
                  <h1 className="text-5xl xl:text-6xl font-bold text-gray-900 mb-4 leading-tight">
                    {item.itemName}
                  </h1>
                  <p className="text-gray-600 text-xl leading-relaxed">
                    {item.description}
                  </p>
                </div>

                {/* Price */}
                <div className="bg-gradient-to-r from-bandiwala-orange to-bandiwala-red bg-clip-text text-transparent">
                  <p className="text-5xl font-bold">₹{selectedSubcategory?.price || 0}</p>
                </div>

                {/* Plate Size Selector */}
                <div className="space-y-4">
                  <label className="block text-xl font-semibold text-gray-800">
                    Select Plate Size
                  </label>
                  <div className="flex flex-wrap gap-3">
                    {item.subcategories.map((subcategory, index) => (
                      <button
                        key={index}
                        type="button"
                        className={`px-6 py-4 rounded-xl text-base font-medium transition-all duration-200 ${
                          selectedSubcategory?.title === subcategory.title
                            ? 'bg-bandiwala-orange text-white shadow-lg transform scale-105'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-md'
                        }`}
                        onClick={() => setSelectedSubcategory(subcategory)}
                      >
                        <div className="text-center">
                          <div className="font-semibold">{subcategory.title}</div>
                          <div className="text-sm opacity-75">{subcategory.quantity}</div>
                          <div className="font-bold">₹{subcategory.price}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Quantity and Add to Cart */}
                <div className="flex items-center gap-6 pt-6">
                  <div className="flex items-center border-2 border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm">
                    <button
                      className="p-4 hover:bg-gray-50 transition-colors"
                      onClick={() => setQuantity(q => Math.max(1, q - 1))}
                    >
                      <Minus size={24} className="text-gray-600" />
                    </button>
                    <span className="px-8 py-4 font-semibold text-xl min-w-[80px] text-center">
                      {quantity}
                    </span>
                    <button
                      className="p-4 hover:bg-gray-50 transition-colors"
                      onClick={() => setQuantity(q => q + 1)}
                    >
                      <Plus size={24} className="text-gray-600" />
                    </button>
                  </div>

                  <Button
                    className="bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white px-12 py-5 text-xl font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                    onClick={handleAddToCart}
                  >
                    Add to Cart
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section for Desktop - Full Width */}
          <div className="max-w-7xl mx-auto px-12 xl:px-16 pb-16">
            {item && (
              <ReviewsList
                targetType="MenuItem"
                targetId={item._id}
                targetName={item.itemName}
                currentUserId={user?._id}
                isAuthenticated={isAuthenticated}
              />
            )}
          </div>
        </div>
      </main>
    </>
  );
}