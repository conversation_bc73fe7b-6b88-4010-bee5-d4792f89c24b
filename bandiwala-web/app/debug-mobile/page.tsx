'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { menuItemService } from '@/services/api';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function MobileDebugPage() {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testSlug, setTestSlug] = useState('sevi-puri');
  const [testResult, setTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const params = useParams();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Collect debug information
    const info = {
      userAgent: navigator.userAgent,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio,
      isMobile: window.innerWidth < 768,
      isTouch: 'ontouchstart' in window,
      params: params,
      searchParams: Object.fromEntries(searchParams.entries()),
      location: {
        href: window.location.href,
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash
      },
      apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/',
      timestamp: new Date().toISOString()
    };
    
    setDebugInfo(info);
    console.log('Mobile Debug Info:', info);
  }, [params, searchParams]);

  const testSlugFetch = async () => {
    setLoading(true);
    setTestResult(null);
    
    try {
      console.log(`Testing slug fetch for: ${testSlug}`);
      const result = await menuItemService.getMenuItemBySlug(testSlug);
      setTestResult({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
      console.log('Test result:', result);
    } catch (error) {
      console.error('Test error:', error);
      setTestResult({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Header />
      <main className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Mobile Debug Page</h1>
        
        {/* Device Information */}
        <div className="bg-white rounded-lg p-6 mb-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">Device Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Screen Size:</strong> {debugInfo.screenWidth}x{debugInfo.screenHeight}
            </div>
            <div>
              <strong>Device Pixel Ratio:</strong> {debugInfo.devicePixelRatio}
            </div>
            <div>
              <strong>Is Mobile:</strong> {debugInfo.isMobile ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Touch Support:</strong> {debugInfo.isTouch ? 'Yes' : 'No'}
            </div>
            <div className="md:col-span-2">
              <strong>User Agent:</strong> 
              <div className="text-xs mt-1 break-all">{debugInfo.userAgent}</div>
            </div>
          </div>
        </div>

        {/* URL Information */}
        <div className="bg-white rounded-lg p-6 mb-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">URL Information</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Current URL:</strong> {debugInfo.location?.href}
            </div>
            <div>
              <strong>Pathname:</strong> {debugInfo.location?.pathname}
            </div>
            <div>
              <strong>Search:</strong> {debugInfo.location?.search || 'None'}
            </div>
            <div>
              <strong>Hash:</strong> {debugInfo.location?.hash || 'None'}
            </div>
          </div>
        </div>

        {/* API Configuration */}
        <div className="bg-white rounded-lg p-6 mb-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">API Configuration</h2>
          <div className="text-sm">
            <div>
              <strong>API URL:</strong> {debugInfo.apiUrl}
            </div>
          </div>
        </div>

        {/* Slug Test */}
        <div className="bg-white rounded-lg p-6 mb-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">Slug Fetch Test</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Test Slug:</label>
              <input
                type="text"
                value={testSlug}
                onChange={(e) => setTestSlug(e.target.value)}
                className="border border-gray-300 rounded px-3 py-2 w-full max-w-xs"
                placeholder="Enter slug to test"
              />
            </div>
            <button
              onClick={testSlugFetch}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Slug Fetch'}
            </button>
            
            {testResult && (
              <div className="mt-4">
                <h3 className="font-semibold mb-2">Test Result:</h3>
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Available Test Slugs */}
        <div className="bg-white rounded-lg p-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">Available Test Slugs</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
            {['sevi-puri', 'pani-puri', 'bhel-puri', 'dahi-puri', 'vada-pav', 'chicken-biryani', 'mutton-biryani', 'vegetable-biryani'].map(slug => (
              <button
                key={slug}
                onClick={() => setTestSlug(slug)}
                className="text-left p-2 border rounded hover:bg-gray-50"
              >
                {slug}
              </button>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
